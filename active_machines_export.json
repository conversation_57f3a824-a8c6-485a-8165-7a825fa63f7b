{"GPU 3090 2": {"child_name": "RTX 3090 | Server 2 | CPU E5-2667 v3", "slot_usage": "0 / 2 Slot", "is_over_limit": false}, "GPU 3090": {"child_name": "RTX 3090 | Server 1 | CPU E5-2690 v4", "slot_usage": "2 / 2 Slot", "is_over_limit": false}, "GPU P40 3": {"child_name": "P40 | Server 3 | CPU E5-2667 v3", "slot_usage": "1 / 1 Slot", "is_over_limit": false}, "GPU P40 Dual 1": {"child_name": "P40 x 2 | Server 1 | CPU E5-2667 v3", "slot_usage": "1 / 1 Slot", "is_over_limit": false}, "GPU P40 2": {"child_name": "P40 | Server 2 | CPU E5-2680 v3", "slot_usage": "2 / 2 Slot", "is_over_limit": false}, "GPU P40": {"child_name": "P40 | Server 1 | CPU E5-2699 v3", "slot_usage": "2 / 2 Slot", "is_over_limit": false}, "GPU P40 4": {"child_name": "P40 x 2 | Server 2 | CPU E5-2667 v3", "slot_usage": "0 / 1 Slot", "is_over_limit": false}, "GPU 5090 1": {"child_name": "RTX 5090 | Server 1 | CPU E5-2699 v3", "slot_usage": "0 / 1 Slot", "is_over_limit": false}}