import json
import os
from pyVim.connect import SmartConnect, Disconnect
from pyVmomi import vim
import ssl

def load_config(config_file):
    with open(config_file, 'r') as f:
        return json.load(f)

def get_active_machines_by_datacenter(config):
    # Disable SSL certificate verification
    context = ssl.SSLContext(ssl.PROTOCOL_TLSv1_2)
    context.verify_mode = ssl.CERT_NONE

    # Connect to vSphere
    si = SmartConnect(
        host=config['vsphere']['host'],
        user=config['vsphere']['user'],
        pwd=config['vsphere']['password'],
        port=config['vsphere']['port'],
        sslContext=context
    )

    # Get the content object
    content = si.RetrieveContent()

    # Get all datacenters
    datacenters = content.rootFolder.childEntity

    active_machines_by_datacenter = {}
    datacenter_config = config.get('datacenter_config', {})
    
    # Get the list of datacenter names from the config file
    configured_datacenter_names = set(datacenter_config.keys())

    for datacenter in datacenters:
        if isinstance(datacenter, vim.Datacenter) and datacenter.name in configured_datacenter_names:
            active_machines = []
            
            # Get all VMs in the datacenter
            vm_view = content.viewManager.CreateContainerView(datacenter, [vim.VirtualMachine], True)
            
            for vm in vm_view.view:
                if vm.runtime.powerState == vim.VirtualMachinePowerState.poweredOn:
                    active_machines.append(vm.name)
            
            active_count = len(active_machines)
            dc_config = datacenter_config.get(datacenter.name, {})
            limit = dc_config.get('limit', float('inf'))
            child_name = dc_config.get('child_name', 'Unknown')
            
            # Special case for Datacenter GPU P40
            if datacenter.name == "GPU P40":
                adjusted_count = max(0, active_count - 3)
                slot_usage = f"{adjusted_count} / {limit} Slot"
                is_full = adjusted_count >= limit
            else:
                slot_usage = f"{active_count} / {limit} Slot"
                is_full = active_count >= limit
            
            active_machines_by_datacenter[datacenter.name] = {
                "child_name": child_name,
                "slot_usage": slot_usage,
                "is_full": is_full
            }

    # Disconnect from vSphere
    Disconnect(si)

    return active_machines_by_datacenter

def export_to_json(data, filename):
    # Ensure the directory exists
    os.makedirs(os.path.dirname(filename), exist_ok=True)
    
    with open(filename, 'w') as f:
        json.dump(data, f, indent=2)

# Usage example
if __name__ == "__main__":
    config = load_config('vsphere_config.json')
    result = get_active_machines_by_datacenter(config)

    # Get export path from config or use default
    export_path = config.get('export_path', '')
    default_filename = "active_machines_export.json"
    
    if export_path:
        export_filename = os.path.join(export_path, default_filename)
    else:
        export_filename = default_filename

    export_to_json(result, export_filename)

    print(f"Active machines data has been exported to {export_filename}")

# Optional: Print a summary to the console
for datacenter, data in result.items():
    print(f"Datacenter: {datacenter}")
    print(f"Child Name: {data['child_name']}")
    print(f"Slot Usage: {data['slot_usage']}")
    print(f"Is Full: {'Yes' if data['is_full'] else 'No'}")
    print()
